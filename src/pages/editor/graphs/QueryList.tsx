import { useState, useRef } from 'react';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
// import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { PLACEMENT } from 'baseui/popover';
import { GsqlQueryMeta, QuerySyntax } from '@tigergraph/tools-models';
import { getQuerySyntax } from '@/utils/query';
import { CypherInstalled, CypherUninstalled, GSQLInstalled, GSQLUninstalled } from '@/pages/editor/graphs/icons';

import StatefulPopover from '@/pages/editor/StatefulPopover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { QueriesIcon } from '@/pages/home/<USER>';
import QueryPopoverContent from '@/pages/editor/graphs/QueryPopoverContent';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { MdAdd, MdUpload } from 'react-icons/md';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { InstallAll, InstallAllRef } from '@/pages/editor/query/InstallAll';
import { expand } from 'inline-style-expand-shorthand';
import { WorkspaceT } from '@/pages/workgroup/type';
import { useQueries } from '@/utils/useQueries';
import { LoadingIndicator } from '@/components/loading-indicator';

export interface QueryListProps {
  wp: WorkspaceT;
  graphName: string;
  expanded: boolean;
  searchText?: string;
  createTempFile: CreateTempFileFn;
}

export default function QueryList({ wp, graphName, expanded, searchText = '', createTempFile }: QueryListProps) {
  const [localExpanded, setLocalExpanded] = useState(false);
  const [css, theme] = useStyletron();
  const installAllRef = useRef<InstallAllRef>(null);

  // Fetch queries when expanded
  const { data: queries = [], isLoading } = useQueries(wp, graphName, localExpanded);

  // Filter queries based on search text
  const filteredQueries = queries.filter((query) => query.name.toLowerCase().includes(searchText.toLowerCase()));
  const uninstalledQueries = filteredQueries.filter((query) => !query.installed);

  const createQuery = (syntax: QuerySyntax) => {
    const queryName = 'new_query';
    const newGSQLQueryContent =
      `CREATE OR REPLACE DISTRIBUTED QUERY ${queryName}(/* Parameters here */) FOR GRAPH ${graphName} { \n` +
      `  /* Write query logic here */ \n  PRINT "${queryName} works!"; \n}`;
    const newOpenCypherQueryContent =
      `CREATE OR REPLACE DISTRIBUTED OPENCYPHER QUERY ${queryName}(/* Parameters here */) FOR GRAPH ${graphName} { \n` +
      `  /* Write query logic here */ \n  RETURN "${queryName} works!" \n}`;

    createTempFile(true, queryName, syntax === 'GSQL' ? newGSQLQueryContent : newOpenCypherQueryContent, graphName);
  };

  const headerContent = (
    <div className="flex items-center justify-between grow">
      <ListItemLabel icon={<QueriesIcon />} label={'Queries'} />
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <>
            <DropdownMenuTrigger asChild>
              <Button kind="text" overrides={{ BaseButton: { style: { ...expand({ padding: 0 }) } } }}>
                <MdAdd size={20} color={theme.colors['icon.primary']} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onSelect={() => createQuery('GSQL')}>GSQL</DropdownMenuItem>
              <DropdownMenuItem onSelect={() => createQuery('CYPHER')}>openCypher</DropdownMenuItem>
            </DropdownMenuContent>
          </>
        </DropdownMenu>

        <Button
          onClick={(e) => {
            installAllRef.current?.installAll(uninstalledQueries);
            e.stopPropagation();
          }}
          kind="text"
          disabled={uninstalledQueries.length === 0}
          overrides={{ BaseButton: { style: { ...expand({ padding: 0 }) } } }}
        >
          <MdUpload size={20} color={theme.colors['icon.primary']} />
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Expandable
        label={headerContent}
        defaultExpanded={localExpanded}
        onExpandChange={(isExpanded) => setLocalExpanded(isExpanded)}
      >
        {isLoading ? (
          <LoadingIndicator />
        ) : (
          filteredQueries.map((query) => (
            <StatefulPopover
              key={query.name}
              content={<QueryPopoverContent query={query} graphName={graphName} createTempFile={createTempFile} />}
              placement={PLACEMENT.right}
              ignoreBoundary={false}
              animateOutTime={200}
              dismissOnClickOutside
            >
              <ListItem>
                <ListItemLabel icon={<QueryIcon query={query} />} label={query.name} />
              </ListItem>
            </StatefulPopover>
          ))
        )}
      </Expandable>
      <InstallAll ref={installAllRef} graphName={graphName} queries={uninstalledQueries} />
    </>
  );
}

function QueryIcon({ query }: { query: GsqlQueryMeta }) {
  switch (true) {
    case query.installed && getQuerySyntax(query) === 'GSQL':
      return <GSQLInstalled />;
    case query.installed && getQuerySyntax(query) === 'CYPHER':
      return <CypherInstalled />;
    case !query.installed && getQuerySyntax(query) === 'GSQL':
      return <GSQLUninstalled />;
    case !query.installed && getQuerySyntax(query) === 'CYPHER':
      return <CypherUninstalled />;
  }

  return null;
}
